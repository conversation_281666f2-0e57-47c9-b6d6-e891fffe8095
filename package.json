{"name": "blockbucket", "version": "1.0.0", "description": "BlockBucket for WordPress block supported by Gutenberg Editor.", "author": "GutenSuite", "homepage": "https://blockbucket.com", "license": "GPL-2.0-or-later", "keywords": ["wordpress", "<PERSON><PERSON>", "blocks", "gutenberg blocks development", "wordpress gutenberg blocks development", "wordpress gutenberg blocks", "wordpress gutenberg blocks development boilerplate"], "prettier": "@wordpress/prettier-config", "stylelint": {"extends": "@wordpress/stylelint-config/scss"}, "scripts": {"build": "wp-scripts build", "format": "wp-scripts format", "lint:css": "wp-scripts lint-style", "lint:js": "wp-scripts lint-js", "packages-update": "wp-scripts packages-update", "plugin-zip": "wp-scripts plugin-zip", "start": "wp-scripts start"}, "devDependencies": {"@babel/plugin-transform-class-properties": "^7.25.9", "@babel/plugin-transform-runtime": "^7.26.9", "@babel/preset-env": "^7.26.9", "@babel/preset-react": "^7.26.3", "@wordpress/babel-preset-default": "^8.18.0", "@wordpress/eslint-plugin": "^22.8.0", "@wordpress/prettier-config": "^4.22.0", "@wordpress/scripts": "^26.12.0", "@wordpress/stylelint-config": "^21.1.0", "babel-plugin-module-resolver": "^5.0.2", "copy-webpack-plugin": "^12.0.2", "mini-css-extract-plugin": "^2.9.2"}, "dependencies": {"@babel/plugin-proposal-class-properties": "^7.18.6", "@wordpress/components": "^29.7.0", "@wordpress/dom-ready": "^4.17.0", "@wordpress/element": "^6.17.0", "react-icons": "^5.5.0", "react-image-magnifiers": "^1.4.0", "vanilla-tilt": "^1.8.1", "vanilla-tilt-react": "^1.0.0"}}