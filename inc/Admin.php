<?php
namespace BlockBucket;

use BlockBucket\Admin\Assets;
use BlockBucket\Admin\Menu;
use BlockBucket\Admin\Ajax;

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly
}


class Admin {
	public function __construct() {
		$this->admin_menu();
		$this->add_assets();
		$this->dispatch_actions();
	}

	public function admin_menu() {
		new Menu();
	}

	public function add_assets() {
		new Assets();
	}

	public function dispatch_actions() {
		new Ajax();
	}
}
