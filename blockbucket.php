<?php
/**
 * Plugin Name:       BlockBucket
 * Description:       BlockBucket for WordPress block supported by G<PERSON>nberg Editor.
 * Requires at least: 6.0
 * Requires PHP:      7.4
 * Version:           1.0.0
 * Author:            GutenSuite
 * License:           GPL-2.0-or-later
 * License URI:       https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain:       blockbucket
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly
}

if ( file_exists( __DIR__ . '/vendor/autoload.php' ) ) {
	require_once __DIR__ . '/vendor/autoload.php';
}

if ( ! class_exists( 'BlockBucket ' ) ) {

	final class BlockBucket {

		const VERSION = '1.0.0';

		protected static $instance = null;

		/**
		 * Constructor
		 *
		 * @return void
		 */
		public function __construct() {
			$this->blockbucket_define_constants();

			register_activation_hook( __FILE__, array( $this, 'blockbucket_activate' ) );
			add_action( 'plugins_loaded', array( $this, 'blockbucket_on_plugins_loaded' ) );
			add_action( 'blockbucket_loaded', array( $this, 'blockbucket_init_plugin' ) );
		}

		/**
		 * On Plugins Loaded hook call
		 *
		 * @return void
		 */
		public function blockbucket_on_plugins_loaded() {
			do_action( 'blockbucket_loaded' );
		}

		/**
		 * Initialize the plugin
		 *
		 * @return void
		 */
		public function blockbucket_init_plugin() {
			if ( is_admin() ) {
				new \BlockBucket\Admin();
			}
			new \BlockBucket\BlockLoader();
		}

		/**
		 * Definte the plugin constants
		 *
		 * @return void
		 */
		public function blockbucket_define_constants() {
			define( 'BLOCKBUCKET_VERSION', '1.0.0' );
			define( 'BLOCKBUCKET_DIR', __DIR__ );
			define( 'BLOCKBUCKET_URL', plugin_dir_url( __FILE__ ) );
			define( 'BLOCKBUCKET_PATH', plugin_dir_path( __FILE__ ) );
			define( 'BLOCKBUCKET_ASSETS_URL', BLOCKBUCKET_URL . 'assets/' );
			define( 'BLOCKBUCKET_ASSETS_PATH', BLOCKBUCKET_PATH . 'assets/' );
		}

		/**
		 * Initialize the plugin
		 *
		 * @return \BlockBucket
		 */
		public static function init() {
			if ( is_null( self::$instance ) ) {
				self::$instance = new self();
			}
			return self::$instance;
		}

		/**
		 * Activation hook call
		 *
		 * @return void
		 */
		public function blockbucket_activate() {
			update_option(
				'blockbucket_activation_flag',
				array(
					'last_activation_time' => time(),
				)
			);

			if ( ! empty( get_option( 'blockbucket_active_blocks', array() ) ) ) {
				return;
			}

			$helper      = new \BlockBucket\Helper();
			$blocks_list = $helper->blockbucket_get_blocks_list();

			$default_active = array_filter(
				$blocks_list,
				function ( $item ) {
					return ! empty( $item['active'] );
				}
			);

			$default_active = array_keys( $default_active );
			update_option( 'blockbucket_active_blocks', $default_active );
		}
	}


	/**
	 * Initialize the plugin
	 *
	 * @return \BlockBucket
	 */

	if ( ! function_exists( 'blockbucket_blocks_init' ) ) {
		function blockbucket_blocks_init() {
			return BlockBucket::init();
		}

		// kick-off the plugin.
		blockbucket_blocks_init();
	}
}
