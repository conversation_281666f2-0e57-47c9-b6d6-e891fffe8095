import { registerBlockType } from '@wordpress/blocks';
import './style.scss';

import attributes from './attributes';
import metadata from './block.json';
import example from "./example";
/**
 * Internal dependencies
 */
import Edit from './edit';

/**
 * Block Registration
 */
registerBlockType( metadata, {
	icon: {
		src: (
			<svg
				width="24"
				height="24"
				viewBox="0 0 24 24"
				fill="none"
				xmlns="http://www.w3.org/2000/svg"
			>
				<path
					d="M3 0.5H21C22.3807 0.5 23.5 1.61929 23.5 3V21C23.5 22.3807 22.3807 23.5 21 23.5H3C1.61929 23.5 0.5 22.3807 0.5 21V3C0.5 1.61929 1.61929 0.5 3 0.5Z"
					fill="white"
					stroke="#5D5FEF"
				/>
				<rect x="3.3" y="13.3" width="9.4" height="3.4" fill="white" />
				<rect
					x="3.3"
					y="13.3"
					width="9.4"
					height="3.4"
					stroke="#5D5FEF"
					stroke-width="0.6"
				/>
				<line
					x1="3"
					y1="6.6"
					x2="16.6"
					y2="6.6"
					stroke="#5D5FEF"
					stroke-width="0.8"
				/>
				<g clip-path="url(#clip0_60_18)">
					<rect x="14" y="13" width="6" height="4" fill="white" />
					<g filter="url(#filter0_d_60_18)">
						<line
							x1="16"
							y1="14.8"
							x2="18"
							y2="14.8"
							stroke="#5D5FEF"
							stroke-width="0.4"
						/>
					</g>
				</g>
				<rect
					x="14.3"
					y="13.3"
					width="5.4"
					height="3.4"
					stroke="#5D5FEF"
					stroke-width="0.6"
				/>
				<defs>
					<clipPath id="clip0_60_18">
						<rect x="14" y="13" width="6" height="4" fill="white" />
					</clipPath>
				</defs>
			</svg>
		),
	},
	attributes,
	edit: Edit,
	 example: example,
} );
